{% extends 'base_updated.html' %}
{% load static %}

{% block title %}تعديل صلاحيات المستخدم - نظام الدولية{% endblock %}

{% block page_title %}تعديل صلاحيات المستخدم{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard' %}">إدارة المستخدمين</a></li>
<li class="breadcrumb-item active">تعديل صلاحيات المستخدم</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 col-lg-6 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-cog me-2 text-primary"></i>
                    تعديل صلاحيات المستخدم: {{ user_to_edit.username }}
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="user-info mb-4">
                    <div class="user-avatar mb-3 text-center">
                        <div class="avatar bg-primary text-white mx-auto">
                            {{ user_to_edit.username|slice:":1" }}
                        </div>
                    </div>
                    <div class="row text-center">
                        <div class="col-md-6 mb-3">
                            <span class="d-block text-muted">اسم المستخدم</span>
                            <span class="fw-bold fs-5">{{ user_to_edit.username }}</span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <span class="d-block text-muted">البريد الإلكتروني</span>
                            <span class="fw-bold">{{ user_to_edit.email }}</span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <span class="d-block text-muted">الاسم الكامل</span>
                            <span class="fw-bold">{{ user_to_edit.first_name }} {{ user_to_edit.last_name }}</span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <span class="d-block text-muted">الحالة الحالية</span>
                            <span class="badge {% if user_to_edit.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {% if user_to_edit.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="row g-3">
                        <!-- Role -->
                        <div class="col-12 mb-3">
                            <label for="{{ form.Role.id_for_label }}" class="form-label required-field">الدور</label>
                            {{ form.Role }}
                            {% if form.Role.errors %}
                            <div class="invalid-feedback d-block">{{ form.Role.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Active Status -->
                        <div class="col-12 mb-3">
                            <label class="form-label">الحالة</label>
                            <div class="form-check">
                                {{ form.IsActive }}
                                <label class="form-check-label" for="{{ form.IsActive.id_for_label }}">
                                    نشط
                                </label>
                            </div>
                            {% if form.IsActive.errors %}
                            <div class="invalid-feedback d-block">{{ form.IsActive.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i> الرجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- User Activity -->
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>
                    نشاط المستخدم
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="timeline p-4">
                    {% if user_activities %}
                        {% for activity in user_activities %}
                        <div class="timeline-item">
                            <div class="timeline-badge">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="card mb-0">
                                <div class="card-body py-3">
                                    <h5 class="mb-1 fs-6">{{ activity.title }}</h5>
                                    <p class="text-muted mb-1 small">{{ activity.date }}</p>
                                    <p class="mb-0">{{ activity.description }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle fa-2x mb-3 text-muted"></i>
                            <p class="mb-0">لا توجد أنشطة حديثة لهذا المستخدم</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form.needs-validation');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
</script>

<style>
    .avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
    }
</style>
{% endblock %}
